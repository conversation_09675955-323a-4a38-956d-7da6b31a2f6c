/**
 * 词库管理主组件
 * 🎯 核心价值：统一的词库管理界面，支持29个词库的可视化管理
 * 📦 功能范围：词库列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import React, { memo, useCallback, useState } from 'react';
import Button from '@/components/ui/Button';
import WordInput from '@/components/ui/WordInput';
import type { 
  BasicColorType, 
  DataLevel, 
  WordLibraryKey 
} from '@/core/matrix/MatrixTypes';
import { 
  COLOR_DISPLAY_ORDER, 
  AVAILABLE_WORD_LIBRARIES,
  getWordLibraryDisplayName,
  getWordLibraryBackgroundColor,
  getWordLibraryTextColor
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';

// ===== 组件属性 =====

interface WordLibraryManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
}

// ===== 折叠按钮组件 =====

interface CollapseButtonProps {
  collapsed: boolean;
  onClick: () => void;
  color: BasicColorType;
}

const CollapseButton: React.FC<CollapseButtonProps> = ({ collapsed, onClick, color }) => {
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(backgroundColor);

  return (
    <button
      onClick={onClick}
      className="w-6 h-6 rounded-full border flex items-center justify-center text-xs font-bold mr-2 flex-shrink-0 transition-all duration-200 hover:scale-110"
      style={{
        backgroundColor: collapsed ? backgroundColor : 'transparent',
        color: collapsed ? textColor : backgroundColor,
        borderColor: backgroundColor,
        borderWidth: collapsed ? '1px' : '2px'
      }}
      title={collapsed ? '展开词库' : '折叠词库'}
    >
      {collapsed ? '●' : '○'}
    </button>
  );
};

// ===== 词库项组件 =====

interface WordLibraryItemProps {
  color: BasicColorType;
  level: DataLevel;
  libraryKey: WordLibraryKey;
}

const WordLibraryItem: React.FC<WordLibraryItemProps> = ({ color, level, libraryKey }) => {
  const { 
    getLibrary, 
    toggleLibraryCollapse,
    getStatistics
  } = useWordLibraryStore();

  const library = getLibrary(libraryKey);
  const displayName = getWordLibraryDisplayName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(backgroundColor);

  const handleToggleCollapse = useCallback(() => {
    toggleLibraryCollapse(libraryKey);
  }, [libraryKey, toggleLibraryCollapse]);

  if (!library) return null;

  return (
    <div className="mb-3">
      {/* 词库标题栏 */}
      <div className="flex items-center mb-2">
        <CollapseButton
          collapsed={library.collapsed}
          onClick={handleToggleCollapse}
          color={color}
        />
        <div 
          className="flex-1 px-3 py-1 rounded-md text-sm font-medium"
          style={{ 
            backgroundColor: backgroundColor + '20', 
            color: backgroundColor,
            border: `1px solid ${backgroundColor}40`
          }}
        >
          <span>{displayName}</span>
          <span className="ml-2 text-xs opacity-70">
            ({library.words.length} 词)
          </span>
        </div>
      </div>

      {/* 词库输入框 */}
      <div className="ml-8">
        <WordInput
          libraryKey={libraryKey}
          color={color}
          level={level}
          collapsed={library.collapsed}
          placeholder={`输入${displayName}词语...`}
        />
      </div>
    </div>
  );
};

// ===== 统计信息组件 =====

const StatisticsPanel: React.FC = () => {
  const { getStatistics } = useWordLibraryStore();
  const stats = getStatistics();

  return (
    <div className="bg-gray-50 rounded-md p-3 mb-4">
      <h4 className="text-sm font-medium text-gray-700 mb-2">词库统计</h4>
      <div className="grid grid-cols-3 gap-4 text-xs text-gray-600">
        <div className="text-center">
          <div className="font-bold text-lg text-blue-600">{stats.totalWords}</div>
          <div>总词数</div>
        </div>
        <div className="text-center">
          <div className="font-bold text-lg text-green-600">{stats.totalLibraries}</div>
          <div>词库数</div>
        </div>
        <div className="text-center">
          <div className="font-bold text-lg text-orange-600">{stats.duplicateWords}</div>
          <div>重复词</div>
        </div>
      </div>
    </div>
  );
};

// ===== 主组件 =====

const WordLibraryManagerComponent: React.FC<WordLibraryManagerProps> = ({
  className = '',
  style,
  showStatistics = true
}) => {
  const [showAllLibraries, setShowAllLibraries] = useState(false);
  const { resetAllLibraries, exportData, importData } = useWordLibraryStore();

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `词库数据_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词库吗？此操作不可撤销。')) {
      resetAllLibraries();
    }
  }, [resetAllLibraries]);

  // 获取要显示的词库列表
  const librariesToShow = showAllLibraries 
    ? AVAILABLE_WORD_LIBRARIES 
    : AVAILABLE_WORD_LIBRARIES.slice(0, 10);

  return (
    <div className={`word-library-manager ${className}`} style={style}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">词库管理</h3>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            title="导出词库数据"
          >
            导出
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleImport}
            title="导入词库数据"
          >
            导入
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={handleReset}
            title="清空所有词库"
          >
            清空
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      {showStatistics && <StatisticsPanel />}

      {/* 词库列表 */}
      <div className="space-y-1">
        {librariesToShow.map(({ color, level }) => {
          const libraryKey = `${color}-${level}` as WordLibraryKey;
          return (
            <WordLibraryItem
              key={libraryKey}
              color={color}
              level={level}
              libraryKey={libraryKey}
            />
          );
        })}
      </div>

      {/* 显示更多按钮 */}
      {AVAILABLE_WORD_LIBRARIES.length > 10 && (
        <div className="mt-4 text-center">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowAllLibraries(!showAllLibraries)}
          >
            {showAllLibraries ? '收起' : `显示全部 (${AVAILABLE_WORD_LIBRARIES.length})`}
          </Button>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-3 bg-blue-50 rounded-md text-xs text-blue-700">
        <div className="font-medium mb-1">使用说明：</div>
        <ul className="space-y-1">
          <li>• 点击圆形按钮可折叠/展开词库</li>
          <li>• 输入词语后用逗号分隔或按回车确认</li>
          <li>• 重复词语会用橙色边框标识</li>
          <li>• 双击矩阵格子可激活填词功能</li>
        </ul>
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const WordLibraryManager = memo(WordLibraryManagerComponent);

WordLibraryManager.displayName = 'WordLibraryManager';

export default WordLibraryManager;
export type { WordLibraryManagerProps };
