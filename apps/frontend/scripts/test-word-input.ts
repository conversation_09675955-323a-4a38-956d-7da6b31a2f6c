/**
 * 填词功能测试脚本
 * 🎯 核心价值：验证双击激活、键盘导航、词语绑定等功能的正确性
 * 📦 功能范围：自动化测试填词功能的各个环节
 * 🔄 架构设计：基于Playwright的端到端测试
 */

import { test, expect } from '@playwright/test';

test.describe('填词功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问主页面
    await page.goto('/');
    
    // 等待页面加载完成
    await page.waitForSelector('.matrix-container');
    
    // 添加一些测试词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      
      // 添加红色1级词语
      store.addWord('red-1', '测试');
      store.addWord('red-1', '词语');
      store.addWord('red-1', '功能');
      
      // 添加蓝色2级词语
      store.addWord('blue-2', '验证');
      store.addWord('blue-2', '检查');
      store.addWord('blue-2', '确认');
    });
  });

  test('双击激活填词功能', async ({ page }) => {
    // 找到一个有颜色和级别的单元格
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    
    // 双击单元格
    await cell.dblclick();
    
    // 验证WordSelector组件是否显示
    await expect(page.locator('[role="listbox"]')).toBeVisible();
    
    // 验证词语列表是否显示
    await expect(page.locator('[role="option"]')).toHaveCount.greaterThan(0);
  });

  test('键盘导航选择词语', async ({ page }) => {
    // 双击激活填词功能
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 等待WordSelector显示
    await page.waitForSelector('[role="listbox"]');
    
    // 测试右箭头键导航
    await page.keyboard.press('ArrowRight');
    
    // 验证选中的词语是否改变
    const selectedOption = page.locator('[role="option"][aria-selected="true"]');
    await expect(selectedOption).toBeVisible();
    
    // 测试左箭头键导航
    await page.keyboard.press('ArrowLeft');
    
    // 再次验证选中状态
    await expect(selectedOption).toBeVisible();
  });

  test('确认选择词语并绑定', async ({ page }) => {
    // 双击激活填词功能
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 等待WordSelector显示
    await page.waitForSelector('[role="listbox"]');
    
    // 按回车确认选择
    await page.keyboard.press('Enter');
    
    // 验证WordSelector是否隐藏
    await expect(page.locator('[role="listbox"]')).not.toBeVisible();
    
    // 验证单元格是否显示词语（需要切换到word模式）
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });
    
    // 验证单元格内容是否包含词语
    await expect(cell).not.toBeEmpty();
  });

  test('ESC键取消填词', async ({ page }) => {
    // 双击激活填词功能
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 等待WordSelector显示
    await page.waitForSelector('[role="listbox"]');
    
    // 按ESC取消
    await page.keyboard.press('Escape');
    
    // 验证WordSelector是否隐藏
    await expect(page.locator('[role="listbox"]')).not.toBeVisible();
  });

  test('删除单元格词语', async ({ page }) => {
    // 先绑定一个词语
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    await page.waitForSelector('[role="listbox"]');
    await page.keyboard.press('Enter');
    
    // 再次双击激活填词功能
    await cell.dblclick();
    await page.waitForSelector('[role="listbox"]');
    
    // 按Delete键删除词语
    await page.keyboard.press('Delete');
    
    // 验证WordSelector是否隐藏
    await expect(page.locator('[role="listbox"]')).not.toBeVisible();
    
    // 验证单元格词语是否被删除
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });
    
    // 验证单元格内容是否为空
    await expect(cell).toBeEmpty();
  });

  test('词语绑定持久化', async ({ page }) => {
    // 绑定一个词语
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    await page.waitForSelector('[role="listbox"]');
    await page.keyboard.press('Enter');
    
    // 刷新页面
    await page.reload();
    await page.waitForSelector('.matrix-container');
    
    // 切换到word模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });
    
    // 验证词语绑定是否持久化
    await expect(cell).not.toBeEmpty();
  });

  test('多个单元格词语绑定', async ({ page }) => {
    // 绑定多个单元格的词语
    const cells = [
      page.locator('[data-x="5"][data-y="5"]').first(),
      page.locator('[data-x="6"][data-y="6"]').first(),
      page.locator('[data-x="7"][data-y="7"]').first()
    ];
    
    for (const cell of cells) {
      await cell.dblclick();
      await page.waitForSelector('[role="listbox"]');
      await page.keyboard.press('Enter');
      await page.waitForTimeout(100); // 短暂等待
    }
    
    // 切换到word模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });
    
    // 验证所有单元格都有词语
    for (const cell of cells) {
      await expect(cell).not.toBeEmpty();
    }
  });
});
