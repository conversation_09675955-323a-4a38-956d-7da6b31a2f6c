/**
 * 手动测试设置脚本
 * 🎯 核心价值：为填词功能提供测试数据和环境设置
 * 📦 功能范围：初始化词库数据，设置测试环境
 * 🔄 架构设计：浏览器控制台脚本，便于手动测试
 */

// 在浏览器控制台中运行此脚本来设置测试环境

declare global {
  interface Window {
    setupWordInputTest: () => void;
    testWordInput: () => void;
    clearTestData: () => void;
  }
}

// 设置测试环境
window.setupWordInputTest = function() {
  console.log('🚀 开始设置填词功能测试环境...');
  
  try {
    // 动态导入store（在实际环境中需要根据具体的模块系统调整）
    const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
    const { useMatrixStore } = require('@/core/matrix/MatrixStore');
    
    const wordStore = useWordLibraryStore.getState();
    const matrixStore = useMatrixStore.getState();
    
    // 清除现有数据
    wordStore.resetAllLibraries();
    matrixStore.clearAllWordBindings();
    
    // 添加测试词语数据
    console.log('📝 添加测试词语...');
    
    // 红色1级词语
    wordStore.addWord('red-1', '测试');
    wordStore.addWord('red-1', '词语');
    wordStore.addWord('red-1', '功能');
    wordStore.addWord('red-1', '验证');
    
    // 蓝色2级词语
    wordStore.addWord('blue-2', '检查');
    wordStore.addWord('blue-2', '确认');
    wordStore.addWord('blue-2', '审核');
    wordStore.addWord('blue-2', '评估');
    
    // 绿色3级词语
    wordStore.addWord('green-3', '分析');
    wordStore.addWord('green-3', '研究');
    wordStore.addWord('green-3', '探索');
    wordStore.addWord('green-3', '发现');
    
    // 黄色1级词语
    wordStore.addWord('yellow-1', '创建');
    wordStore.addWord('yellow-1', '构建');
    wordStore.addWord('yellow-1', '开发');
    wordStore.addWord('yellow-1', '设计');
    
    console.log('✅ 测试环境设置完成！');
    console.log('📋 测试步骤：');
    console.log('1. 双击任意有颜色的单元格激活填词功能');
    console.log('2. 使用左右箭头键导航选择词语');
    console.log('3. 按回车键确认选择');
    console.log('4. 按ESC键取消选择');
    console.log('5. 按Delete键删除已绑定的词语');
    console.log('6. 运行 testWordInput() 进行自动测试');
    
  } catch (error) {
    console.error('❌ 设置测试环境失败:', error);
    console.log('💡 请确保在正确的页面环境中运行此脚本');
  }
};

// 自动测试函数
window.testWordInput = function() {
  console.log('🧪 开始自动测试填词功能...');
  
  try {
    const { useWordInputStore } = require('@/core/wordLibrary/WordLibraryStore');
    const { useMatrixStore } = require('@/core/matrix/MatrixStore');
    
    const wordInputStore = useWordInputStore.getState();
    const matrixStore = useMatrixStore.getState();
    
    // 测试1: 激活填词模式
    console.log('🔍 测试1: 激活填词模式');
    wordInputStore.activateWordInput(5, 5, 'red', 1);
    
    if (wordInputStore.isActive) {
      console.log('✅ 填词模式激活成功');
    } else {
      console.log('❌ 填词模式激活失败');
    }
    
    // 测试2: 选择词语
    console.log('🔍 测试2: 选择词语');
    const selectedWord = wordInputStore.confirmWordSelection();
    
    if (selectedWord) {
      console.log('✅ 词语选择成功:', selectedWord.text);
      
      // 测试3: 绑定词语到单元格
      console.log('🔍 测试3: 绑定词语到单元格');
      matrixStore.bindWordToCell(5, 5, selectedWord.id);
      
      const boundWord = matrixStore.getCellWord(5, 5);
      if (boundWord === selectedWord.id) {
        console.log('✅ 词语绑定成功');
      } else {
        console.log('❌ 词语绑定失败');
      }
      
    } else {
      console.log('❌ 词语选择失败');
    }
    
    // 退出填词模式
    wordInputStore.deactivateWordInput();
    
    // 测试4: 切换到word模式查看结果
    console.log('🔍 测试4: 切换到word模式');
    matrixStore.setContentMode('word');
    
    console.log('✅ 自动测试完成！');
    console.log('💡 请检查坐标(5,5)的单元格是否显示了绑定的词语');
    
  } catch (error) {
    console.error('❌ 自动测试失败:', error);
  }
};

// 清除测试数据
window.clearTestData = function() {
  console.log('🧹 清除测试数据...');
  
  try {
    const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
    const { useMatrixStore } = require('@/core/matrix/MatrixStore');
    
    const wordStore = useWordLibraryStore.getState();
    const matrixStore = useMatrixStore.getState();
    
    // 清除词库数据
    wordStore.resetAllLibraries();
    
    // 清除词语绑定
    matrixStore.clearAllWordBindings();
    
    // 重置矩阵模式
    matrixStore.setContentMode('blank');
    
    console.log('✅ 测试数据清除完成！');
    
  } catch (error) {
    console.error('❌ 清除测试数据失败:', error);
  }
};

// 显示使用说明
console.log('📖 填词功能测试脚本已加载');
console.log('🚀 运行 setupWordInputTest() 开始测试');
console.log('🧪 运行 testWordInput() 进行自动测试');
console.log('🧹 运行 clearTestData() 清除测试数据');

export {};
